<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];
$data = obtener_parameter("data", "POST");
$arrdata = explode("-_-", $data);
$filename = $arrdata[0];
//$id_file = $arrdata[1];
$id_process = $arrdata[1];
$date_received = $arrdata[2];

//$path = "../loadFiles/";
//$lines = file($path . $filename);
//$date_now = return_date_now();
?>
<div class="alert_big">
<?php
if (!empty($_COOKIE['cookie']) && !empty($company)) {
	if (validacion_quota_sms()) {
		$id = insertarMtDesdeTablaTemporal($id_process , return_date_after_gmt($date_received));
		if($id > 0){
			$status = "PROCESSED";
			updateProcessedFile($id_process, $status);
			$result = updateDetailProcessToProcessed($id_process, $status);
			
//			add_process_file($company, "UPLOAD", "PENDING", trim($filename), $login);
//			$process = return_process($company, "UPLOAD");
			?>
			<img src="images/icons/confirm.png">
			El archivo <b><?= $filename ?></b> ha sido cargado exitosamente para su posterior envio.
			<?php
		}else{
			?>
			<img src="images/icons/error.png"/> Error al ingresar los SMS.
			<?php
		}
	}else{
		?>
		<img src="images/icons/error.png"/> Se ha excedido la quota de mensajes disponibles, contacte al administrador.
		<?php
	}
}else{
	?>
	<img src="images/icons/alert.png"> Su sesi&oacute;n ha expirado. Vuelva a ingresar <a href="index.php">aqu&iacute;</a>
	<?php
}
//$action = $company . " ==> " . utf8_decode($filename);
//log_tmc($company, "Envio SMS", "Desde Archivo", $action, $login);
?>
</div>