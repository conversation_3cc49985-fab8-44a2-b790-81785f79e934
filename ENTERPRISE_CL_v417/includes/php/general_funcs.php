<?php

function dbMysql($server, $db) {
    if (!($link = mysql_connect($server, "entersql", "pass.mobid"))) {
        echo "Error conectando a la base de datos.";
        exit();
    }

    if (!mysql_select_db($db, $link)) {
        echo "Error seleccionando la base de datos.";
        exit();
    }

    return $link;
}

function validacion_quota_sms() {
    $company = $_SESSION["id"];
    $max_msg = $_SESSION["max_msg"];
    if ($max_msg == -1) {
        return true;
    } else {
        $query = " select "
                . " cm.max_total_msgs as max, "
                . " (select count(*) from trafficMT t where t.company=cm.id) as actual "
                . " from company cm "
                . " where cm.id=$company ";
        $sql = mysql_query($query);
        $row = mysql_fetch_array($sql);
        $max = $row['max'];
        $actual = $row['actual'];
        if ($actual < $max) {
            return true;
        }
    }
    return false;
}

function obtener_parameter($parameter, $method) {
    $result = '';
    if (strcmp($method, "GET") == 0) {
        if (isset($_GET[$parameter])) {
            $result = $_GET[$parameter];
        }
    } else if (strcmp($method, "POST") == 0) {
        if (isset($_POST[$parameter])) {
            $result = $_POST[$parameter];
        }
    }
    return $result;
}

function add_process($company, $input, $target, $login) {
    if (empty($target)) {
        $target = "(NULL)";
    }
    if (empty($login)) {
        $login = $_SESSION["user"];
    }
    $query = " INSERT into process ("
            . " company, timestamp, input_mode, target, content, login"
            . " ) values ("
            . " '$company', UTC_TIMESTAMP(), '$input', '$target', 'TRANS', '$login'"
            . " )";
    mysql_query($query);
}

function add_process_file($company, $input, $stage, $target, $login) {
    if (empty($target)) {
        $target = 'NULL';
    }
    mysql_query("INSERT into process (company, timestamp, input_mode, target,content,login) values ('$company', UTC_TIMESTAMP(), '$input', '$target','$stage','$login')");
}

function del_process($company, $id_process) {
    mysql_query("DELETE FROM process WHERE company = '$company' AND id = '$id_process'");
}

function validate_format_phone($phone, $format) {
    $regex_default = '/^(\52|\+52|521|\+521)[ -]*([0-9]?[ -]*){9}[0-9]$/';
    $regex_CL = '/^(\+56|56)([0-9]){9}$/';
    $regex_PE = '/^(\+51|51)([0-9]){9}$/';
    $regex_MX = '/^(\52|521)([0-9]){10}$/';
    $response = false;
    $regex = "";
    switch ($format) {
        case "CL":
            $regex = $regex_CL;
            break;
        case "PE":
            $regex = $regex_PE;
            break;
        case "MX":
            $regex = $regex_MX;
            break;
    }
    if (!empty($regex)) {
        if (preg_match($regex, $phone, $matchPhone)) {
            $response = true;
        }
    }
    return $response;
}

function validate_format_datetime($fecha) {
    /*
     * expresion regular para validar fecha en formato 
     * 31/12/2017 23:59:59 or 31-12-2017 23:59:59
     * $regexFecha = '/^([0-2][0-9]|3[0-1])(\/|-)(0[1-9]|1[0-2])\2(\d{4})(\s)([0-1][0-9]|2[0-3])(:)([0-5][0-9])(:)([0-5][0-9])$/';
     */

    /*
     * expresion regular para validar fecha en formato 
     * 31-12-2017 23:59:59
     */
    $response = false;
    $regexFecha = '/^([0-2][0-9]|3[0-1])(-)(0[1-9]|1[0-2])\2(\d{4})(\s)([0-1][0-9]|2[0-3])(:)([0-5][0-9])(:)([0-5][0-9])$/';
    if (preg_match($regexFecha, $fecha, $matchFecha)) {
        $response = true;
    }
    return $response;
}

function validate_time_available_send_message($fecha) {
    $response = false;
    $company = $_SESSION["id"];
    if (!empty($fecha)) {
        $query = "SELECT fn_common_validate_date_aviable_send('$fecha',$company) as resp";
        $sql = mysql_query($query);
        $row = mysql_fetch_array($sql);
        $resp = $row['resp'];
        if (!empty($resp) && $resp > 0) {
            $response = true;
        }
    }
    return $response;
}

function convertirOffset($offset, $inversor) {
    $minutos = 60;
    if ($offset > 0) {
        $offset = $offset * $minutos * $inversor;
        return "$offset";
    } else if ($offset < 0) {
        $offset = $offset * $minutos * $inversor;
        return "$offset";
    } else {
        return "0";
    }
}

function insertTempFile($name_file, $date_local_file) {
    $id = 0;
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];
    $query = "INSERT INTO load_file ("
            . "name_file, company, login, "
            . "date_local, "
            . "date_gmt "
            . ") VALUES ( "
            . "'$name_file', $company, '$login', "
            . "STR_TO_DATE('$date_local_file', '%d-%m-%Y %H:%i:%s'), "
            . "UTC_TIMESTAMP() "
            . ") ";
    mysql_query($query);
    $id = mysql_insert_id();
    return $id;
}

function updateStatusProcess($id_process, $error_text, $status) {
    $query = " UPDATE process "
            . " SET "
            . " error_msg = '$error_text' "
            . " , content = '$status' "
            . " WHERE id = $id_process ";
    mysql_query($query);
}

function updateProcessFile($filename_tmp, $id_process, $count_rows, $error_text, $status) {
    $query = " UPDATE process "
            . " SET "
            . " cont_rows = $count_rows "
            . " , error_msg = '$error_text' "
            . " , target = '$filename_tmp' "
            . " , content = '$status' "
            . " WHERE id = $id_process ";
    mysql_query($query);
}

function updateProcessedFile($id_process, $status) {
    $query = " UPDATE process "
            . " SET "
            . " content = '$status' "
            . " WHERE id = $id_process ";
    mysql_query($query);
}

function insertDetailTableTemporal($data_insert) {
    $query = "INSERT INTO detail_process( "
            . " id_process, "
            . " number_process, "
            . " date_local_process, "
            . " date_gmt_process, "
            . " msg_process"
            . ", carrier_process "
            . ", status_detprocess "
            . " ) VALUES $data_insert ";
    mysql_query($query);
    $id = mysql_insert_id();
    return $id;
}

function updateDetailProcessToProcessed($id_process, $status) {
    $result = 0;
    if ($id_process > 0 && !empty($status)) {
        $query = " UPDATE detail_process "
                . "SET status_detprocess = '$status' "
                . "WHERE id_process = $id_process";
        mysql_query($query);
        $result = mysql_affected_rows();
    }
    return $result;
}

function insertarMtDesdeTablaTemporal($id_process, $date_received) {
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];

//	$content = $filename;
//	add_process($company, "UPLOAD", $content, $login);
//	$process = return_process($company, "UPLOAD");

    $query = "INSERT INTO trafficMT ("
            . "company, recipientId, recipientDomain"
            . ", status, receivedTime, dispatchTime"
            . ", login, input_process, input_mode, msgText"
            . ") ("
            . " SELECT "
            . " $company, d.number_process, d.carrier_process "
            . ", 'QUEUED', '$date_received', STR_TO_DATE(d.date_gmt_process, '%d-%m-%Y %H:%i:%s') "
            . ", '$login', $id_process, 'UPLOAD', d.msg_process "
            . " FROM detail_process d "
            . " WHERE id_process = $id_process )";
    mysql_query($query);
    $id = mysql_insert_id();
    return $id;
}

/*
  Estimado Sr ==parametro1== le informamos que tiene ==parametro2== hasta el ==parametro3== para realizar compras
 */

function add_process_uploadfile($input_mode, $fileName, $count_rows, $date_local) {
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];

    $query = " INSERT into process ("
            . " company, timestamp, input_mode, target, content, login, date_gmt, cont_rows"
            . " ) values ("
            . " $company, '$date_local', '$input_mode', '$fileName', 'PENDING', '$login', UTC_TIMESTAMP(), $count_rows"
            . " )";
    mysql_query($query);
    $id = mysql_insert_id();
    return $id;
}

function validate_permisson($codigo) {
    $login = $_SESSION["user"];
    $query = ""
            . " SELECT "
            . " count(*) as cant "
            . " FROM account a  "
            . " INNER JOIN account_profile ac ON a.id = ac.id_account "
            . " INNER JOIN profile p2 ON ac.id_profile = p2.id_profile "
            . " INNER JOIN profile_permisson pp ON p2.id_profile = pp.id_profile "
            . " INNER JOIN permisson p ON p.id_permisson = pp.id_permisson "
            . " WHERE a.login = '$login' "
            . " AND p.cod_permisson = '$codigo' ";

    $cant = 0;
    $sql = mysql_query($query);
    if (!empty($sql)) {
        $row = mysql_fetch_array($sql);
        $cant = $row['cant'];
    }
    return $cant;
}

function get_count_row_query($query) {
    $textsql = "";
    $cant = 0;
    $textsql .= "SELECT count(*) AS cant FROM (";
    $textsql .= "$query";
    $textsql .= ") AS T";
    $sql = mysql_query($textsql);
    if (!empty($sql)) {
        $row = mysql_fetch_array($sql);
        $cant = $row['cant'];
    }
    return $cant;
}

function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length / strlen($x)))), 1, $length);
}

function sendMailPass($pass, $mail) {
    $codeApp = "app1";
    $subject = "Clave De Sistema";
    $to = $mail;

    $body = "";
    $body .= "<html>";
    $body .= "<body>";
    $body .= "<div>";
    $body .= "Su nueva clave es: <b>$pass</b>";
    $body .= "</div>";
    $body .= "</body>";
    $body .= "</html>";

    $listMails[0] = $to;
    $listTo = array(
        "listMails" => $listMails
    );
    $listMails[0] = "";
    $listCc = array(
        "listMails" => $listMails
    );
    $listMails[0] = "";
    $listCco = array(
        "listMails" => $listMails
    );

    $mail = array(
        "codApp" => $codeApp,
        "subject" => $subject,
        "toAddress" => $listTo,
        "ccAddress" => $listCc,
        "ccoAddress" => $listCco,
        "messageBody" => $body
    );
    $list = array();
    $list[0] = $mail;
    $request = array(
        "listItemRequest" => $list
    );
//	$response = callPostRest($request);
    return true;
}

function resetPassword($id) {
    $token = $_SESSION["TOKEN"];
    $url = "";
    if (defined("URL_SERVICE_ACCOUNT")) {
        $url = URL_SERVICE_ACCOUNT;
    }
    $response = "";
    if (strlen($url) > 0) {
        $request = curl_init($url . "/api/user/resetPass/" . $id);
        curl_setopt($request, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($request, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json;charset=UTF-8'
            , 'Authorization: ' . $token
        ));
//		curl_setopt($request, CURLOPT_POSTFIELDS, $json);
        curl_setopt($request, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($request);
        curl_close($request);
    }
    return $response;
}

function obtenerEncoding($ruta) {
    $encoding = "";
    $arr = "";
    $cmd = 'file -bi ' . $ruta;
    $retorno_cmd = shell_exec($cmd);
    $arr = explode(";", $retorno_cmd);
    $arr = explode("=", $arr[1]);
    $encoding = trim($arr[1]);
    return $encoding;
}

function modificarEncoding($path, $file, $encoding) {
    $source = "";
    $target = "";
    $source = $path . '/' . $file;
    $target_file = 'utf-8_' . $file;
    $target = $path . '/' . $target_file;
    if ($encoding == "utf-8") {
        $cmd_mv = "mv $source $target";
        shell_exec($cmd_mv);
    } else {
        $cmd_iconv = "iconv -t utf-8 -f $encoding -o $target $source";
        shell_exec($cmd_iconv);
    }
    return $target_file;
}

function obtenerParametros($selMessage) {
    $regex = "/==P[0-9]==/i";
    $coincidencias = "";
    preg_match_all($regex, $selMessage, $coincidencias, PREG_SET_ORDER);
    $arrParametros = [];

    foreach ($coincidencias as $valor) {
        $arrParametros[] = $valor[0];
    }
    return $arrParametros;
}

function quitarEnter($texto) {
//	$buscar=array(chr(13).chr(10), "\r\n", "\n", "\r");
    $buscar = array("\r\n", "\n", "\r");
    $reemplazar = array("", "", "");
    $response = str_ireplace($buscar, $reemplazar, $texto);
    return $response;
}

function textoNombreColumnasFileCampaing($arrParametros) {
    $texto = "Móvil";
    for ($i = 0; $i < count($arrParametros); $i++) {
        $texto .= "; $arrParametros[$i]";
    }
    return $texto;
}

function insertCampaign($company, $name, $startDate, $endDate, $file, $reagendar, $rows) {
    $id = 0;
    $login = $_SESSION["user"];

    $query = "";
    $query .= " INSERT INTO campaign ( ";
    $query .= " id_account ";
    $query .= " , id_company ";
    $query .= " , description_campaign ";
    $query .= " , date_create_campaign ";
    $query .= " , date_start_campaign ";
    $query .= " , date_end_campaign ";
    $query .= " , reagendar ";
    $query .= " , file_campaign ";
    $query .= " , status ";
    $query .= " , total_campaign ";
    $query .= " ) VALUES ( ";
    $query .= " (SELECT id FROM account where login='$login') ";
    $query .= " , $company ";
    $query .= " , '$name' ";
    $query .= " , UTC_TIMESTAMP() ";
    $query .= " , '$startDate' ";
    $query .= " , '$endDate' ";
    $query .= " , $reagendar ";
    $query .= " , '$file' ";
    $query .= " , 'PENDING' ";
    $query .= " , $rows ";
    $query .= " ) ";

    mysql_query($query);
    $id = mysql_insert_id();
    return $id;
}

function insertDetailCampaign($id_process, $id_campaign) {
    $status = "'PENDING'";

    $query = "";
    $query .= " INSERT INTO detail_campaign ( ";
    $query .= " id_campaign ";
    $query .= " , number_detcamp ";
    $query .= " , message_detcamp ";
    $query .= " , status_detcamp ";
    $query .= " )( ";
    $query .= " SELECT ";
    $query .= " $id_campaign ";
    $query .= " , number_process ";
    $query .= " , msg_process ";
    $query .= " , $status ";
    $query .= " FROM detail_process ";
    $query .= " WHERE id_process = $id_process ";
    $query .= " ) ";

    mysql_query($query);
    $id = mysql_insert_id();
    return $id;
}

function reeplaceParametersMsg($parameterData, $parameterName, $msgtext, $col_fijas) {
    $valor = "";
    for ($i = 0; $i < count($parameterName); $i++) {
        $parameter = $parameterName[$i];
        $valor = $parameterData[$i + $col_fijas];
        $msgtext = str_replace($parameter, $valor, $msgtext);
    }
    return $msgtext;
}

function redirectLogOff() {
    $server = $_SERVER["SERVER_NAME"];
    $root_site = "ENTERPRISE_CL_v417/";
    $url = "$server/$root_site/expire_session.php";
    header('Location: ' . $url);
}

?>
