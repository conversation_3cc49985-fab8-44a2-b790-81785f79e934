<?php
include ('dbConn.php');
?>

<?php
//////////////////////////////////////////////////////////////////// 
/////////////////// A P R O V I S I O N A M I E N TO ////////////////// 
/////////////////////////////////////////////////////////////////////// 
?>

<?php

function provUserSearch() {

    title("Aprovisionamiento", "Buscar Abonados", "aprovisionamiento");
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];
    ?>

    <form name="tmc" action="">
        <table width="440" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td width="100"><b>Celular</b></td>
                <td ><?php input_phone(''); ?></td>
            </tr>
            <tr>
                <td width="100"><b>Nombre</b></td>
                <td><input type="text" name="first_name" size="30" /></td>
            </tr>
            <tr>
                <td width="100"><b>Apellido</b></td>
                <td><input type="text" name="last_name" size="30" /></td>
            </tr>
            <tr>
                <td align="left" valign="top"></td>
                <td align="left" valign="bottom" height="30"> 
                    <span onclick="provUserSearch();" class="buttom">Buscar</span> 
                    <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
            </tr>
        </table>
    </form>
    <br />
    <br />

    <div id="resultado"></div>

    <?php
}
?>


<?php

function provUsers() { ?>

<?php
    global $dbh; // Acceder a la conexión PDO global
    title("Aprovisionamiento", "Todos los Abonados", "aprovisionamiento");
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];


    if (!isset($_GET['p'])) {
        $page = 1;
    } else {
        $page = $_GET['p'];
    }

    $rows_for_page = 30;

    $rows_from = (($page * $rows_for_page) - $rows_for_page);

    $interval = convertirOffset($_SESSION["OFFSET_COMPANY"], -1);

    $query = "";
    $query .= "SELECT ";
    $query .= " phone ";
    $query .= " ,carrier ";
    $query .= " ,name1 ";
    $query .= " ,name2 ";
    $query .= " ,login ";
    $query .= " , date_sub( created, interval $interval minute) as created  ";
    $query .= "FROM provUser ";
    $query .= "WHERE company = $company";

    // Contar el total de filas usando PDO
    $stmt_count = $dbh->prepare($query);
    $stmt_count->execute();
    $total_rows = $stmt_count->rowCount();

    $total_pages = ceil($total_rows / $rows_for_page);

    // Consulta paginada usando PDO
    $query_paginated = "$query ORDER by created DESC LIMIT :rows_from, :rows_for_page";
    $stmt = $dbh->prepare($query_paginated);
    $stmt->bindParam(':rows_from', $rows_from, PDO::PARAM_INT);
    $stmt->bindParam(':rows_for_page', $rows_for_page, PDO::PARAM_INT);
    $stmt->execute();
    $sql = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $url = "?seccion=provUsers&";
    ?>

    <div class="alert_big">
        Existen <b><?= $total_rows; ?></b> abonados(s) creado(s) para esta compañia. <?php if ($total_rows == 0) { ?> 
            <br/>
            <br/>
            Para comenzar a crear abonados ingrese 
            <a href="?seccion=provUserAdd"><b>aquí</b></a>.<?php } ?></div>
    <br />

    <a href="?seccion=provUserSearch" class="styleButtonOne">
        <span class="buttom">Buscar Abonado</span>
    </a>  
    <a href="?seccion=provUserAdd" class="styleButtonOne">
        <span class="buttom">Crear Abonado</span>
    </a>

    <?php if ($total_rows > 0) { ?>

        <br /><br />
        <?php pag_pages($page, $total_pages, $url); ?>
        <br /><br />

        <table width="100%" border="0" cellspacing="0" cellspacing="0" class="table-list">
            <tr>
                <th>M&Oacute;VIL</th>
                <th>OPERADOR</th>
                <th>NOMBRE</th>
                <th>APELLIDO</th>
                <th>CREADO POR</th>
                <th>FECHA CREACI&Oacute;N</th>
                <th>ACCI&Oacute;N</th>
            </tr>
            <?php
            $i = 0;
            foreach ($sql as $row) {
                $res = $i % 2;
                if ($res == 0) {
                    $class = "";
                } else {
                    $class = "table-list-tr";
                }
                ?> 
                <tr class="<?= $class ?>">
                    <td class="baseTd"><?= $row['phone'] ?></td>
                    <td class="baseTd"><?= $carrier = return_carrier_name($row['carrier']); ?></td>
                    <td class="baseTd"><?= $row['name1'] ?></td>
                    <td class="baseTd"><?= $row['name2'] ?></td>
                    <td class="baseTd"><?= $row['login'] ?></td>
                    <td class="baseTd"><?= $row['created'] ?></td>
                    <td class="baseTd">
                        <div id="delete<?= $row['phone'] ?>"> 
                            <a href="?seccion=provUserEdit&id=<?= $row['phone'] ?>" title="Editar Abonado">
                                <img src="images/icons/edit_user.png">
                            </a>  
                            <a href="?seccion=provUserAsoc&phone=<?= $row['phone'] ?>" title="Asociar Abonado a Grupo">
                                <img src="images/icons/list.png" >
                            </a> 
                            <a>
                                <img src="images/icons/delete.png" onclick="provUserDel(<?= $row['phone'] ?>); return false" title="Eliminar Abonado" class="img_button">
                            </a>
                        </div>
                    </td>
                </tr>
                <?php
                $i++;
            }
            ?>
        </table>

        <?php
    }
}
?>


<?php

function provUserAdd() {

    title("Aprovisionamiento", "Agregar Abonado", "aprovisionamiento");
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];
    ?>

    <form name="tmc" action="">
        <table width="100%" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="middle" width="120"><b>M&oacute;vil</b></td>
                <td align="left" valign="middle">
                    <?php input_phone_confirmed(''); ?>
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle"></td>
                <td align="left" valign="middle" ><div style="height: 18px;" id="checkPhone"></div> </td>
            </tr>
            <tr>
                <td align="left"><b>Nombre</b></td>
                <td align="left"><input type="text" name="firstName"  size="32" maxlength="30"></td>
            </tr>
            <tr>
                <td align="left"><b>Apellido</b></td>
                <td align="left"><input type="text" name="lastName"  size="32" maxlength="30"></td>
            </tr>
            <tr>
                <td align="left"><b>¿Asociar a Grupo?</b></td>
                <td align="left" valign="middle">
                    <input type="radio" name="associate" value="N" checked> No 
                    <input type="radio" name="associate" value="Y"> Si
                </td>
            </tr>
            <tr>
                <td align="left"></td>
                <td align="left"><div id="provUserGroupId"></div></td>
            </tr>
            <tr>
                <td align="left" valign="top"></td>
                <td align="left" valign="bottom" height="30"> 
                    <span onclick="provUserAdd();" class="buttom">CREAR ABONADO</span>  
                    <span onclick="reset();" class="buttom">BORRAR</span> 
                    <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="top"></td>
                <td align="left" valign="bottom" height="30">
                    <br/>
                    <br/>
                    <div id="resultado"></div>
                </td>
            </tr>
        </table>
    </form>




    <?php
}
?>


<?php

function provUserEdit() {

    global $dbh;
    title("Aprovisionamiento", "Editar Abonado", "aprovisionamiento");
    $phone = $_GET['id'];
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];


    $stmt = $dbh->prepare("SELECT * FROM provUser WHERE phone = :phone AND company = :company");
    $stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
    $stmt->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

    $stmt_groups = $dbh->prepare("SELECT us.phone, pr.name, us.idGroup FROM UserGroup us JOIN provGroup pr ON us.idGroup = pr.id WHERE us.phone = :phone AND us.company = :company");
    $stmt_groups->bindParam(':phone', $phone, PDO::PARAM_STR);
    $stmt_groups->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt_groups->execute();
    $groups = $stmt_groups->fetchAll(PDO::FETCH_ASSOC);
    $cnt_groups = $stmt_groups->rowCount();
    ?>

    <form name="tmc" action=""  onkeypress="return event.keyCode != 13">
        <table width="100%" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="middle" width="100" height="15"><b>Celular</b></td>
                <td align="left" valign="middle">
                    <b><?= $phone ?></b>
                    <input type="hidden" name="phone" value="<?= $phone ?>">
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle"><b>Nombre</b></td>
                <td align="left" valign="middle">
                    <input type="text" name="firstName"  size="32" maxlength="30" value="<?php echo $row["name1"] ?>">
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle"><b>Apellido</b></td>
                <td align="left" valign="middle">
                    <input type="text" name="lastName"  size="32" maxlength="30" value="<?php echo $row["name2"] ?>">
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle"></td>
                <td align="left" valign="middle">
                    <div id="provUserEditGroupId"> 
                        <?php if ($cnt_groups > 0) { ?>
                            <br />
                            <b>Grupos Asociados</b><br /><br />

                            <table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
                                <tr>
                                    <th>Asociar</th>
                                    <th>Grupo</th>
                                </tr>
                                <?php
                                $i = 0;
                                foreach ($groups as $group_row) {
                                    $res = $i % 2;
                                    $class = ($res == 0) ? "" : "table-list-tr";
                                    ?>
                                    <tr class="<?= $class ?>">
                                        <td><input type="checkbox" name="groups" value="<?= $group_row['idGroup'] . "," ?>" checked></td>
                                        <td><?= $group_row['name']; ?></td>
                                    </tr>
                                    <?php $i++; } ?>
                            </table>
                        } else {
                            ?>
                        </div>
                        <br/>
                        <div class="alert_big">No existen <b>Grupos</b> asociados a este móvil.</div>
                    <?php } ?></td>
            </tr>
            <tr>
                <td align="left" valign="middle"></td>
                <td align="left" valign="bottom" height="30">
                    <span onclick="provUserEdit();" class="buttom">Modificar</span>
                    <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
            </tr>
            <tr>
                <td align="left" valign="middle"></td>
                <td align="left" valign="bottom" height="30">
                    <br/>
                    <br/>
                    <div id="resultado"></div>
                </td>
            </tr>
        </table>
    </form>
    <?php
}
?>

<?php

function provUserAsoc() {

    global $dbh;
    title("Aprovisionamiento", "Asociar Abonado a un Grupo.", "aprovisionamiento");
    $login = $_SESSION["user"];

    $phone = $_GET["phone"];
    $company = $_SESSION["id"];

    // Obtener los IDs de los grupos a los que el usuario ya pertenece
    $stmt1 = $dbh->prepare("SELECT idGroup FROM UserGroup WHERE phone = :phone AND company = :company");
    $stmt1->bindParam(':phone', $phone, PDO::PARAM_STR);
    $stmt1->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt1->execute();
    $arrayGrp = $stmt1->fetchAll(PDO::FETCH_COLUMN, 0);

    // Obtener todos los grupos de la compañía
    $stmt2 = $dbh->prepare("SELECT * FROM provGroup WHERE company = :company");
    $stmt2->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt2->execute();
    $all_groups = $stmt2->fetchAll(PDO::FETCH_ASSOC);
    $num_groups = $stmt2->rowCount();
    ?>


    <form name="tmc" action="">
        <table width="440" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td width="100" align="left" valign="top"><b>Celular</b></td>
                <td align="left" valign="top"><?= $phone ?></td>
            </tr>
            <tr>
                <td width="100" align="left" valign="top"></td>
                <td align="left" valign="top"><br />

                    <div id="groups">
                        <?php if ($num_groups > 0) { ?>
                            <table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
                                <tr>
                                    <th>ASOCIAR</th>
                                    <th>NOMBRE</th>
                                    <th>ACCI&Oacute;N</th>
                                </tr>
                                <?php
                                $i = 0;
                                $checked = "";
                                foreach ($all_groups as $row2) {
                                    $res = $i % 2;
                                    if ($res == 0) {
                                        $class = "";
                                    } else {
                                        $class = "table-list-tr";
                                    }
                                    if (in_array($row2['id'], $arrayGrp)) {
                                        $checked = "checked";
                                    } else {
                                        $checked = "";
                                    }
                                    ?>
                                    <tr class="<?= $class ?>">
                                        <td class="baseTd">
                                            <input type="checkbox" name="groups" onClick="provUserAsoc(this.checked, '<?= $phone; ?>', '<?= $row2['id'] ?>');" <?= $checked ?>>
                                        </td>
                                        <td class="baseTd"><?= $row2['name'] ?></td>
                                        <td class="baseTd">
                                            <div id="idgroup<?= $row2['id']; ?>" style="width:20px;"></div>
                                        </td>
                                    </tr>
                                    <?php
                                    $i++;
                                }
                                ?>
                            </table>
                            <br /><br />
                            <span onclick="javascript:document.location.href = 'mcs.php?seccion=provUsers'" class="buttom">Finalizar</span>
                        <?php } else { ?>
                            <div class="alert_small">
                                No existen <b>Grupos</b> creados.
                                <br/>
                                <br/> Para crear un nuevo Grupo ingrese <a href="?seccion=provGroupAdd">aquí</a>.
                            </div>
                        <?php } ?> 
                    </div>
                </td>
            </tr>
        </table>
    </form>

    <?php
}
?>

<?php

function provGroups() {

    global $dbh;
    title("Aprovisionamiento", "Grupos.", "aprovisionamiento");

    $company = $_SESSION["id"];
    $login = $_SESSION["user"];

    $query = "SELECT pg.*, COUNT(ug.phone) as cnt
              FROM provGroup pg
              LEFT JOIN UserGroup ug ON pg.id = ug.idGroup AND pg.company = ug.company
              WHERE pg.company = :company
              GROUP BY pg.id
              ORDER BY pg.name";
    $stmt = $dbh->prepare($query);
    $stmt->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt->execute();
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $num_groups = $stmt->rowCount();
    ?>

    <div class="alert_big">
        Existen <b><?= $num_groups; ?></b> grupo(s) asociado(s) a esta compa&ntilde;ia.
        <?php if ($num_groups == 0) { ?>
            No existen grupos asociados a esta compa&ntilde;ia. Para agregar un nuevo grupo haz click 
            <a href="?seccion=provGroupAdd"><b>aqu&iacute;</b></a>. 
        <?php } ?>
    </div>
    <br>
    <a href="?seccion=provGroupAdd" class="styleButtonOne">
        <span class="buttom">Crear Grupos</span>
    </a>
    <br>
    <br>
    <?php if ($num_groups > 0) { ?>
        <table width="100%" border="0" cellpadding="0"  cellspacing="0" class="table-list">
            <tr>
                <th>ID</th>
                <th>NOMBRE</th>
                <th>ABONADOS</th>
                <th>CREADO POR</th>
                <th>FECHA CREACI&Oacute;N</th>
                <th>ACCI&Oacute;N</th>
            </tr>
            <?php
            $i = 0;
            foreach ($groups as $row) {
                $res = $i % 2;
                if ($res == 0) {
                    $class = "";
                } else {
                    $class = "table-list-tr";
                }
                ?>
                <tr class="<?php echo $class ?>">
                    <td class="baseTd"><?= $row['id'] ?></td>
                    <td class="baseTd" height="20"><?php echo $row['name'] ?></td>
                    <td class="baseTd"><?= $row['cnt'] ?></td>
                    <td class="baseTd"><?= $row['login'] ?></td>
                    <td class="baseTd"><?= $row['created'] ?></td>
                    <td class="baseTd">
                        <div id="delete<?= $row['id'] ?>">
                            <a href="?seccion=provGroupEdit&id=<?php echo $row['id'] ?>" title="Editar Grupo">
                                <img src="images/icons/edit_group.png" >
                            </a> 
                            <a href="?seccion=provGroupAsoc&idGroup=<?php echo $row['id'] ?>" title="Asociar Abonados">
                                <img src="images/icons/list.png" >
                            </a>
                            <a href="?seccion=provUserLoadFile&idGroup=<?= $row['id']; ?>" title="Cargar Abonados Desde Archivo">
                                <img src="images/icons/upload-file.png" >
                            </a> 
                            <img src="images/icons/delete.png" onClick="provGroupDel(<?= $row['id'] ?>);" title="Eliminar Grupo" class="img_button"> 
                        </div> 
                    </td>
                </tr>
                <?php
                $i++;
            }
            ?>
        </table>
        <?php
    }
}
?>



<?php

function provUserLoadFile() {

    title("Aprovisionar", "Cargar Abonados Desde Archivo.", "send");
    $login = $_SESSION["user"];
    $company = $_SESSION["id"];
    $idGroup = $_GET["idGroup"];
    ?>
    <form action="?seccion=provUserLoadFileValue&idGroup=<?= $idGroup ?>" method="POST" name="tmc" enctype="multipart/form-data">
        <table width="100%" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="top" height="30">Tipo de Archivo</td>
                <td align="left" valign="top"><b>TXT</b> o <b>CSV</b></td>
            </tr>
            <tr>
                <td align="left" valign="top" height="30">Formato de Archivo</td>
                <td align="left" valign="top">Celular;Nombre;Apellido</td>
            </tr>
            <tr>
                <td align="left" valign="top" height="30">Archivo </td>
                <td align="left" valign="top">
                    <input type="file" name="filetxt" >
                    <input type="submit" value="CARGAR" >
                </td>
            </tr>
        </table>
    </form>
    <div id="resultado"></div>
    <?php
}
?>

<?php

function provUserLoadFileValue() {

    title("Aprovisionamiento", "Cargar Abonados Desde Archivo.", "send");
    $max_char = return_max_char();
    ?>

    <table width="700" class="table-general" >
        <tr>
            <td >
                <?php
                $idGroup = $_GET['idGroup'];
                $filename = $_FILES['filetxt']['name'];
                $path = "loadFiles/";
                $fileType = pathinfo($filename);
                $filename_tmp = substr($filename, 0, -4) . "_" . return_date_now_format('dmY_His') . "." . $fileType['extension'];
                $filename_tmp = str_replace(" ", "_", $filename_tmp);



                if ($fileType['extension'] == "txt" || $fileType['extension'] == "csv") {
                    if (move_uploaded_file($_FILES['filetxt']['tmp_name'], $path . $filename_tmp)) {
                        
                    } else {
                        echo "Ocurrió algún error al subir el fichero. No pudo guardarse.";
                        $errForm = "Y";
                    }
                } else {
                    ?>
                    <b>1) <u>Carga de Archivo:</u></b><br>
                    <br> <b>Error:</b> El archivo debe ser <b>.txt</b> o <b>.csv</b>. Favor verificar y volver a cargar 
                    <a href='?seccion=provUserLoadFile&idGroup=<?= $idGroup ?>'><b>aquí</b>
                    </a>.
                    <?php
                    $errForm = "Y";
                }
                ?>

                <?php
                $val = "...";
                $lines = file($path . $filename_tmp);
                $i = 1;

                foreach ($lines as $line_num => $line) {

                    $datos = explode(";", $line);
                    $phone = trim($datos[0]);
                    $name = trim($datos[1]);
                    $lastname = trim($datos[2]);
                    $dateNow = return_date_now_format("Y-m-d H:i:s");

                    if (count($datos) < 3 || count($datos) > 3) {
                        ?>
                        <b>Error:</b> El archivo no cumple el formato establecido <b>Celular; Nombre; Apellido</b> Favor verificar.<br><br>
                        <?php
                        $ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path$filename_tmp";
					    shell_exec($cmd_rm_linux);
                        $errForm = "Y";
                        break;
                    }

                    if (empty($name)) {
                        echo "<b>Error Line $i:</b> Nombre Vacio <br>";
                        $errForm = "Y";
                        $ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path$filename_tmp";
					    shell_exec($cmd_rm_linux);

                    }

                    if (empty($lastname)) {
                        echo "<b>Error Line $i:</b> Apellido Vacio <br>";
                        $errForm = "Y";
                        $ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path$filename_tmp";
					    shell_exec($cmd_rm_linux);
                    }
                    $size_number = $_SESSION["SIZE_NUMBER_MOBILE"];
                    if (empty($phone)) {
                        echo "<b>Error Line $i:</b> N&uacute;mero de m&oacute;vil vacio<br>";
                        $errMovil = "Y";
                        $ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path$filename_tmp";
					    shell_exec($cmd_rm_linux);
                    }
                    if (strlen($phone) < $size_number) {
                        echo "<b>Error Line $i:</b> El largo v&aacute;lido debe ser de $size_number digitos<br>";
                        $errMovil = "Y";
                        $ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path$filename_tmp";
					    shell_exec($cmd_rm_linux);
                    }
                    if (strlen($phone) > $size_number) {
                        echo "<b>Error Line $i:</b> Excede el largo n&uacute;merico v&aacute;lido de $size_number digitos.<br>";
                        $errMovil = "Y";
                        $ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path$filename_tmp";
					    shell_exec($cmd_rm_linux);
                    }
                    if (strlen($phone) > $size_number) {
                        echo "<b>Error Line $i:</b> Excede el largo n&uacute;merico v&aacute;lido de $size_number digitos.<br>";
                        $errMovil = "Y";
                        $ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path$filename_tmp";
					    shell_exec($cmd_rm_linux);
                    }
                    ?>
                    <?php
                    $i++;
                }
                ?> 

                <?php if (empty($errForm) && empty($errMovil)) { ?>
                    <b><u>Validaci&oacute;n OK:</u> 
                        <img src="images/icons/confirm.png" />
                    </b>
                    <br>
                    <br>
                    <div id="resultado"><br><br>
                        <form name="tmc" action="">
                            <input type="hidden" name="filetxt" value="<?= $filename_tmp ?>">
                            <span onclick="provUserLoadFile('<?= $idGroup; ?>');" class="buttom">APROVISIONAR</span> 
                            <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
                        </form>
                    </div>
                <?php } else { ?>

                    <br/>
                    <div class="alert_big">Favor validar el archivo y volver a cargar.</div>
                    <br>
                    <br>
                    <span onclick="javascript:history.back();" class="buttom">Volver</span>
                <?php } ?>
            </td>
        </tr>
    </table>


<?php } ?>


<?php

function provGroupAdd() {

    title("Aprovisionamiento", "Crear Nuevo Grupo.", "aprovisionamiento");
    $login = $_SESSION["user"];
    $company = $_SESSION["id"];
    ?>

    <form name="tmc" action="" onkeypress="return event.keyCode != 13">

        <table width="100%" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="top" width="120">
                    <b>Nombre del Grupo</b>
                </td>
                <td align="left" valign="top" >
                    <input type="text"  name="name" size="28" maxlength="25">
                </td>
            </tr>
            <tr>
                <td align="left"><b>¿Asociar Abonados?</b></td>
                <td align="left" valign="middle">
                    <input type="radio" name="associate" value="N" checked> No 
                    <input type="radio" name="associate" value="Y"> Si
                </td>
            </tr>
            <tr>
                <td align="left"></td>
                <td align="left" valign="middle" id="resultado" height="40">
                    <span onclick="provGroupAdd();" class="buttom">Crear Grupo</span>
                    <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
                </td>
            </tr>
            <tr>
                <td align="left"></td>
                <td align="left" valign="middle" id="provGroupUserId"> </td>
            </tr>
            <tr>
                <td align="left"></td>
                <td align="left" valign="middle" id="provGroupUserNew"> </td>
            </tr>
            <tr>
                <td align="left" valign="top" width="100"></td>
                <td align="left" height="30" valign="bottom">
                </td>
            </tr>
        </table>
    </form>



    <?php
}
?>

<?php

function provGroupEdit() {

    global $dbh;

    title("Aprovisionamiento", "Grupos", "aprovisionamiento");
    $idGroup = $_GET['id'];
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];

    if (isset($_GET['p'])) {
        $p = $_GET['p'];
    } else {
        $p = 1;
    }
    $page = $p;

    $rows_for_page = 3;

    $rows_from = (($page * $rows_for_page) - $rows_for_page);

    $stmtGrp = $dbh->prepare("SELECT * FROM provGroup WHERE id = :idGroup AND company = :company");
    $stmtGrp->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
    $stmtGrp->bindParam(':company', $company, PDO::PARAM_INT);
    $stmtGrp->execute();
    $rowGrp = $stmtGrp->fetch(PDO::FETCH_ASSOC);

    $query_base = "FROM UserGroup us JOIN provUser pr ON us.phone = pr.phone WHERE us.idGroup = :idGroup AND us.company = :company";

    $stmt_count = $dbh->prepare("SELECT count(*) " . $query_base);
    $stmt_count->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
    $stmt_count->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt_count->execute();
    $total_rows = $stmt_count->fetchColumn();

    $query_paged = "SELECT pr.phone, pr.carrier, pr.name1, pr.name2, pr.created " . $query_base . " LIMIT :rows_from, :rows_for_page";
    $stmt = $dbh->prepare($query_paged);
    $stmt->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
    $stmt->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt->bindParam(':rows_from', $rows_from, PDO::PARAM_INT);
    $stmt->bindParam(':rows_for_page', $rows_for_page, PDO::PARAM_INT);
    $stmt->execute();
    $users_in_group = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $total_pages = ceil($total_rows / $rows_for_page);

    $url = "?seccion=provGroupEdit&id=$idGroup";
    ?>	

    <form name="tmc" action=""  onkeypress="return event.keyCode != 13">
        <table width="550"  cellpadding="2" cellspacing="0" class="table-general">
            <tr>
                <td align="left" valign="top" width="100"><b>Nombre</b></td>
                <td align="left" valign="top" > 
                    <?php
                    if (!isset($id)) {
                        $id = 0;
                    }
                    ?>
                    <input type="text"  name="name" size="28" maxlength="100" value="<?= $rowGrp['name'] ?>"> 
                    <input type="hidden" name="idGroup" value="<?= $id ?>">
                    <br/>
                    <br/>
                    <span onclick="provGroupEdit('<?= $idGroup ?>');" class="buttom">Modificar</span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="top" width="100"></td>
                <td align="left" valign="top">
                    <br/>
                    <br/>
                    <div id="resultado"></div>
                    <?php if ($total_rows > 0) { ?>
                        <br/>
                        <b><u>Abonados Asociados</u></b>
                        <br/>
                        <br/>
                        <table width="100%">
                            <tr>
                                <td align="center">
                                    <?php pag_pages($page, $total_pages, $url); ?>
                                </td>
                            </tr>
                        </table>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table-list">
                            <tr>
                                <th>ASOCIAR</th>
                                <th>M&Oacute;VIL</th>
                                <th>OPERADOR</th>
                                <th>NOMBRE COMPLETO</th>
                                <th>ACCI&Oacute;N</th>
                            </tr>
                            <?php
                            $i = 0;
                            $checked = "";
                            foreach ($users_in_group as $row2) {
                                $res = $i % 2;
                                if ($res == 0) {
                                    $class = "";
                                } else {
                                    $class = "table-list-tr";
                                }
                                ?>
                                <tr class="<?php echo $class ?>">
                                    <td class="baseTd">	<input type="checkbox" name="users" value="<?= $row2['phone'] . "," ?>" onClick="provGroupAsoc(this.checked, '<?= $row2['phone'] ?>', '<?= $idGroup ?>');" checked></td>
                                    <td class="baseTd"><?= $row2['phone'] ?></td>
                                    <td class="baseTd"><?php $carrier = return_carrier($row2['phone']); ?> <?= $carrier = return_carrier_name($carrier); ?></td>
                                    <td class="baseTd"><?= ucwords(strtolower($row2['name1'])) ?> <?= ucwords(strtolower($row2['name2'])) ?></td>
                                    <td class="baseTd">
                                        <div style="width:20px;" id="idphone<?= $row2['phone']; ?>"></div>
                                    </td>
                                </tr>
                                <?php
                                $i++;
                            }
                            ?>
                        </table>
                    <?php } else { ?>
                        <div class="alert_big">
                            No existen <b>abonados</b> asociados. Ingrese 
                            <a href="?seccion=provGroupAsoc&idGroup=<?= $idGroup ?>">aqu&iacute;</a> 
                            si desea asociar abonados.
                        </div>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td></td>
                <td align="left" height="30" valign="bottom">
                    <span onclick="javascript:document.location.href = 'mcs.php?seccion=provGroups'" class="buttom">Ir a Grupos</span>
                </td>
            </tr>

        </table>
    </form>
    <?php
}
?>

<?php

function provGroupAsoc() {

    global $dbh;
    title("Aprovisionamiento", "Asociar Abonados al Grupo", "aprovisionamiento");
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];

    $idGroup = obtener_parameter('idGroup', 'GET');

    $p = obtener_parameter('p', 'GET');
    $page = (isset($p) && $p > 0) ? (int)$p : 1;

    $rows_for_page = 30;

    $rows_from = (($page * $rows_for_page) - $rows_for_page);

    // Get group name
    $stmtIdGroup = $dbh->prepare("SELECT name FROM provGroup WHERE id = :idGroup AND company = :company");
    $stmtIdGroup->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
    $stmtIdGroup->bindParam(':company', $company, PDO::PARAM_INT);
    $stmtIdGroup->execute();
    $nameGroup = $stmtIdGroup->fetchColumn();

    // Get phones already in the group
    $stmt1 = $dbh->prepare("SELECT phone FROM UserGroup WHERE idGroup = :idGroup AND company = :company");
    $stmt1->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
    $stmt1->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt1->execute();
    $arrayGrp = $stmt1->fetchAll(PDO::FETCH_COLUMN, 0);

    // Get total users for the company for pagination
    $stmt_count = $dbh->prepare("SELECT COUNT(*) FROM provUser WHERE company = :company");
    $stmt_count->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt_count->execute();
    $total_rows = $stmt_count->fetchColumn();

    // Get paginated users for the company
    $stmt2 = $dbh->prepare("SELECT * FROM provUser WHERE company = :company LIMIT :rows_from, :rows_for_page");
    $stmt2->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt2->bindParam(':rows_from', $rows_from, PDO::PARAM_INT);
    $stmt2->bindParam(':rows_for_page', $rows_for_page, PDO::PARAM_INT);
    $stmt2->execute();
    $all_users = $stmt2->fetchAll(PDO::FETCH_ASSOC);

    $total_pages = ceil($total_rows / $rows_for_page);

    $url = "?seccion=provGroupAsoc&idGroup=$idGroup";
    ?>

    <form name="tmc" action="">
        <table width="100%" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="top" width="100" ><b>Nombre del Grupo</b></td>
                <td align="left" valign="top" ><b><?= $nameGroup ?></b></td>
            </tr>
            <tr>
                <td align="left" valign="top"></td>
                <td align="left" height="30" valign="bottom">
                    <br/>
                    <div class="alert_big">
                        Existen <b><?= $cnt_usr = return_count_user_group($idGroup); ?></b> 
                        abonados(s) asociados a este grupo.
                    </div>
                    <br/>
                    <br/>
                    <table width="603">
                        <tr>
                            <td align="center">
                                <?php pag_pages($page, $total_pages, $url); ?>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td align="left" valign="top"></td>
                <td align="left" height="30" valign="bottom">
                    <div id="users">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table-list">
                            <tr>
                                <th>ASOCIAR</th>
                                <th>M&Oacute;VIL</th>
                                <th>OPERADOR</th>
                                <th>NOMBRE COMPLETO</th>
                                <th>ACCI&Oacute;N</th>
                            </tr>

                            <?php
                            $i = 0;
                            $checked = "";
                            foreach ($all_users as $row2) {
                                $res = $i % 2;
                                if ($res == 0) {
                                    $class = "";
                                } else {
                                    $class = "table-list-tr";
                                }
                                if (in_array($row2['phone'], $arrayGrp)) {
                                    $checked = "checked";
                                } else {
                                    $checked = "";
                                }
                                ?>
                                <tr class="<?php echo $class ?>">
                                    <td class="baseTd">	<input type="checkbox" name="users" value="<?= $row2['phone'] . "," ?>" onClick="provGroupAsoc(this.checked, '<?= $row2['phone'] ?>', '<?= $idGroup ?>');" <?= $checked ?>></td>
                                    <td class="baseTd"><?= $row2['phone'] ?></td>
                                    <td class="baseTd"><?= $row2['carrier']; ?></td>
                                    <td class="baseTd"><?= ucwords(strtolower($row2['name1'])) ?> <?= ucwords(strtolower($row2['name2'])) ?></td>
                                    <td class="baseTd">
                                        <div style="width:20px;" id="idphone<?= $row2['phone']; ?>"></div>
                                    </td>
                                </tr>
                                <?php
                                $i++;
                            }
                            ?>
                        </table>
                    </div></td>
            </tr>
            <tr>
                <td align="left" valign="top" width="100"></td>
                <td align="left" height="30" valign="bottom">
                    <span onclick="javascript: document.location.href = 'mcs.php?seccion=provGroups'" class="buttom">Ir a Grupos</span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="top" width="100"></td>
                <td align="left" height="30" valign="bottom"><br/><br/>
                    <div id="resultado"></div>
                </td>
            </tr>
        </table>
    </form>
    <?php
}
?>
