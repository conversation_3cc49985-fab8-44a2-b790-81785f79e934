
//-----------------------------------------------------------------------

function login () {

	if(document.tmc.userName.value == "") {
		alert("Debe ingresar un Usuario.");
		window.document.tmc.userName.focus();
		return;
	}

	if(document.tmc.userPass.value == "") {
		alert("Debe ingresar una Password.");
		window.document.tmc.userPass.focus();
		return;
	}  

  	document.tmc.submit();
}

function pulsar(e) {
	tecla=(document.all) ? e.keyCode : e.which;
  if(tecla==13) return false;
}

function checkPhone(phone) {
	

	
	div = document.getElementById("checkPhone");
	
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:left;"><img src="images/waiting_small.gif"></p>';
	ajax.open("GET", "ajaxfuncs/checkPhone.php?phone="+phone,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}
	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
	
	
}

			
function sendNow () {
		
	phone = document.tmc.phone.value;
	
	var placeHolder = document.getElementById("placeHolderFormatMobilNumber").value;
	var maxCharMsg = document.getElementById("maxlengMessage").value;
	var maxCharMobil = document.getElementById("maxSizeMobilNumber").value;
	
	var e = document.getElementById("phone");
	if(validaFormatoPhone(e)==false){
		return;
	}	
	
	if(phone == '')
	{
		alert("Debe ingresar un telefono.");
		 window.document.tmc.phone.focus();
		//document.tmc.phone.value="Ej:569XXXXXXXX";
		return false;
	}
	else
	{
		if(isNaN(phone)) {
           alert("Debe ingresar un Celular válido (Numérico)");
                window.document.tmc.phone.focus();
                return false; 
		}
		else if((phone.length < maxCharMobil) || (phone.length > maxCharMobil))
		{
            alert("Debe ingresar un Celular con el formato "+placeHolder);
            window.document.tmc.phone.focus();
		    return false;   
        }
	}
	if(isMessage(document.tmc.msgtext.value) == false) {
		return;
	} 
	div = document.getElementById('resultado');
	msgtext = document.tmc.msgtext.value;
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"/></p>';
	ajax.open("POST", "ajaxfuncs/sendNow.php",true);
	ajax.onreadystatechange=function() {	
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
	}
	document.tmc.phone.value="";
	document.tmc.msgtext.value=""; 
	document.tmc.remLen.value=maxCharMsg;
	}
	ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send("phone="+phone+"&msgtext="+msgtext)
}

//-----------------------------------------------------------------------

			
function sendAfter (dateNow) {
			
    var phone = document.tmc.phone.value;
	
	var placeHolder = document.getElementById("placeHolderFormatMobilNumber").value;
	var maxCharMsg = document.getElementById("maxlengMessage").value;
	var maxCharMobil = document.getElementById("maxSizeMobilNumber").value;
	
	var e = document.getElementById("phone");
	if(validaFormatoPhone(e)==false){
		return;
	}	
	
	if(phone == '')
	{
		alert("Debe ingresar un telefono.");
		 window.document.tmc.phone.focus();
		//document.tmc.phone.value="Ej:569XXXXXXXX";
		return false;
	}
	else
	{
		
		if(isNaN(phone)) {
           alert("Debe ingresar un Celular válido (Numérico)");
                window.document.tmc.phone.focus();
                return false; 
		}
		else if((phone.length < maxCharMobil) || (phone.length > maxCharMobil))
		{
            alert("Debe ingresar un Celular con el formato "+placeHolder);
            window.document.tmc.phone.focus();
		    return false;   
        }
		
		
	}
		
        
	if(isDateAfter(document.tmc.sendDateAfter.value, dateNow) == false) {
		return;
	}

	if(isMessage(document.tmc.msgtext.value) == false) {
		return;
	}

	div = document.getElementById('resultado');
	sendDateAfter = document.tmc.sendDateAfter.value;
	msgtext = document.tmc.msgtext.value;

	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("POST", "ajaxfuncs/sendAfter.php",true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}

	document.tmc.phone.value="";
	document.tmc.msgtext.value=""; 
	document.tmc.remLen.value=maxCharMsg;              

	}

	ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send("phone="+phone+"&sendDateAfter="+sendDateAfter+"&msgtext="+msgtext)
}
			
//------------------------------------------------------------------------

function sendNowGroup () {
	
	var maxCharMsg = document.getElementById("maxlengMessage").value;
	
        if(isGroup(document.tmc.idGroup.value) == false) {
                return;
        }

        if(isMessage(document.tmc.msgtext.value) == false) {
                return;
        }

	div = document.getElementById('resultado');
	idGroup = document.tmc.idGroup.value;
	msgtext = document.tmc.msgtext.value;
//	console.log('valor: '+textGroup);
//	if(textGroup.length > 0){
//		alert('hay texto');
//		return;
//	}
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("POST", "ajaxfuncs/sendNowGroup.php",true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
	}

	document.tmc.idGroup.value="";
	//document.tmc.cont.value="";
	document.tmc.msgtext.value="";
	document.tmc.remLen.value=maxCharMsg;
	}

	ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send("idGroup="+idGroup+"&msgtext="+msgtext)
}

	
function sendAfterGroup (dateNow) {
	
	var maxCharMsg = document.getElementById("maxlengMessage").value;
	
	if(isGroup(document.tmc.idGroup.value) == false) {
		return;
	}

	if(isDateAfter(document.tmc.sendDateAfter.value, dateNow) == false) {
		return;
	}

	if(isMessage(document.tmc.msgtext.value) == false) {
		return;
	}

	div = document.getElementById('resultado');
	
	idGroup 			= document.tmc.idGroup.value;
	sendDateAfter		= document.tmc.sendDateAfter.value;
	msgtext			    = document.tmc.msgtext.value;
  
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("POST", "ajaxfuncs/sendAfterGroup.php",true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}

	document.tmc.idGroup.value="";
	document.tmc.msgtext.value="";
	document.tmc.remLen.value=maxCharMsg;
	}

	ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send("idGroup="+idGroup+"&sendDateAfter="+sendDateAfter+"&msgtext="+msgtext)
}



		
			
//------------------------------------------------------------------------
			
function sendFileConvinated () {


	var div = $('#resultado');
	
	var msgtext = $('#msgtext').val();
	var file = document.getElementById('filetxt').files[0];
	if(msgtext !=null){
		msgtext = msgtext.trim();
		if(msgtext.length>0){
			if(file != null){
				var formData = new FormData();
				formData.append('msgtext', msgtext);
				formData.append('filetxt', file);
				$.ajax({
					url: 'ajaxfuncs/sendConvinatedFileValue.php',
					data: formData,
					type:  'POST',
					encoding:"UTF-8", 
					cache: false,
					contentType: false,
					processData: false,
					beforeSend: function(){
						div.html('<p style="text-align:left;"><img src="images/waiting.gif"></p>');
						$('#btnSend').addClass('oculto');
						$('#btnSend').removeClass('visible');
					},
					success: function(data) 
					{
						$("#resultado").html(data);
						$('#msgtext').val('');
					},
					error: function(data) 
					{
						console.log(data);
						$("#resultado").html("Error al enviar los datos");
					},
					complete: function(data){

					}
				});
			}else{
				alert('Debe seleccionar un archivo.');
			}
		}else{
			$('#msgtext').val('');
			alert('Debe ingresar un mensaje valido.');
		}
	}else{
		alert('Debe ingresar un mensaje.');
	}
	
}
			
//------------------------------------------------------------------------

function sendFile () {

	var div = document.getElementById('resultado');
	var filetxt = document.tmc.filetxt.value;
	var idfile = document.tmc.idfile.value;
	var datelocal = document.tmc.datelocal.value;
	var data = filetxt+"-_-"+idfile+"-_-"+datelocal;
	
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("POST", "ajaxfuncs/sendFile.php",true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
	}
	
	document.tmc.filetxt.value="";
	}

	ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send("data="+data);
}

function sendConvinatedFile() {
	var div = document.getElementById('resultado');
	var filetxt = $('#hddfiletxt').val();
	var idfile = $('#hddidfile').val();
	var datelocal = $('#hdddatelocal').val();
	
	var formData = new FormData();
	formData.append('idfile', idfile);
	formData.append('filetxt', filetxt);
	formData.append('datelocal', datelocal);
	
	$.ajax({
		url: 'ajaxfuncs/sendConvinatedFile.php',
		data: formData,
		type:  'POST',
		encoding:"UTF-8", 
		cache: false,
		contentType: false,
		processData: false,
		beforeSend: function(){
			$('#divloadImgMt').html('<p style="text-align:left;"><img src="images/waiting.gif"></p>');
			$('#btnSendFile').addClass('oculto');
			$('#btnSendFile').removeClass('visible');
		},
		success: function(data) 
		{
			$("#resultado").html(data);
			$('#msgtext').val('');
		},
		error: function(data) 
		{
			console.log(data);
			$("#resultado").html("Error al enviar los datos");
		},
		complete: function(data){
			$('#btnSendFile').addClass('visible');
			$('#btnSendFile').removeClass('oculto');
			$('#btnSend').addClass('visible');
			$('#btnSend').removeClass('oculto');
			$('#filetxt').val('');
		}
	});
}
function redirigir(url){
	window.location.href=url;
}
//------------------------------------------------------------------------


function provUserSearch () {
	
	
	div 			= document.getElementById('resultado');
	phone 			= document.tmc.phone.value;
	first_name 	= document.tmc.first_name.value;
	last_name 	= document.tmc.last_name.value;
	var regular = /^[0-9a-zA-Z]{1,50}$/;

	if(phone == "" && first_name == "" && last_name == "") { 
		alert("Debe rellenar al menos un Dato.");
		window.document.tmc.phone.focus();
		return;
	}

	if(!regular.test(first_name)){
	//alert("pruba esteba");
		div.innerHTML="Nombre sin caracteres especiales";
		window.document.tmc.first_name.focus();
		return;
	}

	if(!regular.test(last_name)){
	div.innerHTML="Apellido sin caracteres especiales";
		windows.document.tmc.last_name.focus();
		return
	}


	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("GET", "ajaxfuncs/provUserSearch.php?phone="+phone+"&first_name="+first_name+"&last_name="+last_name,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}

	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
}

function provUserGroup (action) {

	div = document.getElementById('provUserGroupId');

	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("POST", "ajaxfuncs/provUserGroup.php",true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}

	}

	ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send("action="+action)
}


function provUserAdd () {

	phone		= document.tmc.phone.value;
	firstName	= document.tmc.firstName.value;
	lastName	= document.tmc.lastName.value;
        var regular = /^[0-9a-zA-Z]{1,20}$/;

	var e = document.getElementById("phone");
	if(validaFormatoPhone(e)==false){
		return;
	}

	if(isPhone(document.tmc.phone.value) == false) {
		return;
     }
	
	check		= document.tmc.check_phone.value;	
	
	if(check == "N") {
		alert("Debe ingresar un Móvil distinto.");
		window.document.tmc.phone.focus();
		return;
	}
	
	
	if(check == "U") {
		alert("El Móvil no está asociado a ningún Operador válido.");
		window.document.tmc.phone.focus();
		return;
	}

	if(window.document.tmc.firstName.value == "") {
		alert("Debe ingresar un Nombre.");
		window.document.tmc.firstName.focus();
		return;
	}

	if(window.document.tmc.lastName.value == "") {
		alert("Debe ingresar un Apellido.");
		window.document.tmc.lastName.focus();
		return;
	}

	if(!regular.test(firstName)){
	alert( "Nombre sin caracteres especiales");
		window.document.tmc.firstName.focus();
		return;
	}

	if(!regular.test(lastName)){
	alert("Apellido sin caracteres especiales");
		window.document.tmc.lastName.focus();
		return;
	}
	
	if(document.tmc.associate[0].checked == true) { 
	
		action = "N";

	} else { 
	
		action = "Y";
		
	}
	
	div = document.getElementById('resultado');

	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("GET", "ajaxfuncs/provUserAdd.php?phone="+phone+"&firstName="+firstName+"&lastName="+lastName+"&action="+action,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}

	document.tmc.phone.value="";
	document.tmc.firstName.value="";
	document.tmc.lastName.value="";
	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
}


//------------------------------------------------------------------------
			
function provUserEdit () {

	var regular = /^[0-9a-zA-Z]{1,100}$/;

	if(window.document.tmc.firstName.value == "") {
		alert("Debe ingresar un Nombre.");
		window.document.tmc.firstName.focus();
		return;
	}

	if(window.document.tmc.lastName.value == "") {
		alert("Debe ingresar un Apellido.");
		window.document.tmc.lastName.focus();
		return;
	}
	
	var group = document.getElementById("provUserEditGroupId").getElementsByTagName("INPUT");

	groups = new Array ()
	selected = false;
	for (var i=0; i< group.length; i++) {
		if(group[i].checked != "") {
			 groups[i] = group[i].value;
			 selected = true; 
		}
	}
	
	var groups = groups.join("");

	div = document.getElementById('resultado');
	phone = document.tmc.phone.value;
	firstName = document.tmc.firstName.value;
	lastName = document.tmc.lastName.value;

	if(!regular.test(firstName)){
	alert("Nombre sin caracteres especiales");
		window.document.tmc.firstName.focus();
		return;
	}

	if(!regular.test(lastName)){
	alert("Apellido sin caracteres especiales");
		window.document.tmc.lastName.focus();
		return;
	}

	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("GET", "ajaxfuncs/provUserEdit.php?phone="+phone+"&firstName="+firstName+"&lastName="+lastName+"&groups="+groups,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}
	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
}	
	
//------------------------------------------------------------------------
		
		
		
		function provUserLoadFile (idGroup) {

	div		= document.getElementById('resultado');
	filetxt	= document.tmc.filetxt.value;

	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("POST", "ajaxfuncs/provUserLoadFile.php",true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
	}
	
	document.tmc.filetxt.value="";
	}

	ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send("filetxt="+filetxt+"&idGroup="+idGroup)
}	
			
function provUserDel (phone) { 

	
	var eliminar = confirm("¿Está seguro que desea eliminar este Móvil?")
		if (eliminar) {
	
	div = document.getElementById("delete"+phone);
	
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting_small.gif"></p>';
	ajax.open("GET", "ajaxfuncs/provUserDel.php?phone="+phone,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}
	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
	}
		
}


//------------------------------------------------------------------------

function provGroupAdd () {
			
	name = document.tmc.name.value;
	var regular = /^[0-9a-zA-Z]{1,20}$/
	if(name == "") {
		alert("Debe ingresar un Nombre.");
		window.document.tmc.name.focus();
		return;
	}

	if(document.tmc.associate[0].checked == true) { 
	
		action = "N";

	} else { 
	
		action = "Y";
		
	}

	if(!regular.test(name)){
	alert("Nombre sin caracteres especiales");
		window.document.tmc.name.focus();
		return;
	}


	
	div = document.getElementById('resultado');
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("GET", "ajaxfuncs/provGroupAdd.php?name="+name+"&action="+action,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}
	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
}

function provGroupUser (type_usr, idGroup) {

	
	div = document.getElementById('provGroupUserId');
	
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("GET", "ajaxfuncs/provGroupUser.php?type_usr="+type_usr+"&idGroup="+idGroup,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}

	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
}

function provGroupUserNew (idGroup) {
	
	phone 		= document.tmc.phone.value;	
	nombre 		= document.tmc.nombre.value;	
	apellido 	= document.tmc.apellido.value;	
	var regular = /^[0-9a-zA-Z]{1,50}$/;

	
	var e = document.getElementById("phone");
	if(validaFormatoPhone(e)==false){
		return;
	}	
	
	if(phone == "") {
		alert("Debe ingresar un Celular.");
		window.document.tmc.phone.focus();
		return;
	}

	check		= document.tmc.check_phone.value;	
	
	if(check == "N") {
		alert("Debe ingresar un Móvil distinto.");
		window.document.tmc.phone.focus();
		return;
	}

	if(!regular.test(nombre)){
	alert("Debe ingresar un nombre sin caracteres especiales")
		window.document.tmc.nombre.focus();
		return;
	}

	if(!regular.test(apellido)){
	alert("Debe ingresar un Apellido sin caracteres especiales")
	window.document.tmc.apellido.focus();
		return;
	}

	if(nombre == "") {
		alert("Debe ingresar un Nombre.");
		window.document.tmc.nombre.focus();
		return;
	}
	
	if(apellido == "") {
		alert("Debe ingresar un Apellido.");
		window.document.tmc.apellido.focus();
		return;
	}
	
	div = document.getElementById('provGroupUserNew');
	
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("GET", "ajaxfuncs/provGroupUserNew.php?phone="+phone+"&nombre="+nombre+"&apellido="+apellido+"&idGroup="+idGroup,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}

	}

	document.tmc.phone.value = "";	
	document.tmc.nombre.value = "";	
	document.tmc.apellido.value = "";	


	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
}

function provGroupUserOld (p, idGroup) {
	
	if(p != "") { 
	
		p = "&p="+p;
	}	

	div = document.getElementById('provGroupUserOld');
	
	phone		= document.getElementById("phone").value;
	nombre	= document.getElementById("nombre").value;
	
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("GET", "ajaxfuncs/provGroupUserOld.php?phone="+phone+"&nombre="+nombre+"&idGroup="+idGroup+""+p,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}

	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
}


function provUserAsoc (bool, phone, idGroup) { 

	if(!bool) action = "N"; else action = "Y";

	div = document.getElementById("idgroup"+idGroup);
	
	ajax=nuevoAjax();
	div.innerHTML = '<img src="images/waiting_small.gif">';
	ajax.open("GET", "ajaxfuncs/provUserAsoc.php?phone="+phone+"&action="+action+"&idGroup="+idGroup,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}
	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
	
		
}



function provGroupEdit (idGroup) {
	
		
	name = document.tmc.name.value;
	var regular = /^[0-9a-zA-Z]{1,50}$/;

	if(document.tmc.name.value == "") {
		alert("Debe ingresar un Nombre.");
		window.document.tmc.name.focus();
		return;
	}

	if(!regular.test(name)){
	alert("Debe ingresar un nombre sin caracteres especiales");
		window.document.tmc.name.focus();
		return;
	}
	
	
	div = document.getElementById('resultado');


	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("GET", "ajaxfuncs/provGroupEdit.php?name="+name+"&idGroup="+idGroup,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}
	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
}
			
function provGroupDel (idGroup) {

	var eliminar = confirm("¿Está seguro que desea eliminar este Grupo?")
	
	if (eliminar) {

	div = document.getElementById("delete"+idGroup);
	
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting_small.gif"></p>';
	ajax.open("GET", "ajaxfuncs/provGroupDel.php?idGroup="+idGroup,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}
	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
	}
}


function provGroupAsoc (bool, phone, idGroup) { 

	if(!bool) action = "N"; else action = "Y";

	div = document.getElementById("idphone"+phone);
	
	ajax=nuevoAjax();
	div.innerHTML = '<img src="images/waiting_small.gif">';
	ajax.open("GET", "ajaxfuncs/provGroupAsoc.php?phone="+phone+"&action="+action+"&idGroup="+idGroup,true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
		}
	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
	
		
}

function calendar () { 

			var fecha = new Date();
			var year=fecha.getFullYear();
			Calendar.setup({ inputField  : "sendDateFrom", range       :  [year, 2999],
			ifFormat    : "%Y-%m-%d %H:%M",
			showsTime      :    true,
			timeFormat     :    "24",
			button      : "lanzador"
			}
			)

}

//------------------------------------------------------------------------
			
function gesReport () {
	var listUsuarios = document.getElementById("hdd-id-usuarios").value;
	var txtmsg = document.getElementById("txt-message-js");
	txtmsg.innerHTML ="";
	if(listUsuarios != null && listUsuarios.length > 0){
		var arr = listUsuarios.split('-');
		if(arr.length==0){
			txtmsg.innerHTML = "La lista de usuarios seleccionados esta vacia, favor de seleccionar algun usuario.";
			return;
		}
	}else{
		txtmsg.innerHTML = "La lista de usuarios seleccionados esta vacia, favor de seleccionar algun usuario.";
		return;
	}
	if(isDateFromTo(document.tmc.sendDateFrom.value, document.tmc.sendDateTo.value) == false) {
                return;
	}
	  
	if(isEmpty(document.tmc.phone.value) == true) { 
		
		if(isPhone(document.tmc.phone.value) == false) {
                	return;
        	}
	}
	
	document.tmc.submit();
}
			

//------------------------------------------------------------------------
			
function gesStatus () {
			
	if(isDateFromTo(document.tmc.sendDateFrom.value, document.tmc.sendDateTo.value) == false) {
                return;
	}
	   
	document.tmc.submit();
}

//------------------------------------------------------------------------

function gesFileReport () {
			
	if(isDateFromTo(document.tmc.sendDateFrom.value, document.tmc.sendDateTo.value) == false) {
                return;
	}
	   
	document.tmc.submit();
}

//------------------------------------------------------------------------

function sopPass () {
	
	if(window.document.tmc.oldpassword.value == "") {
		alert("Debe ingresar su Actual Password");
		window.document.tmc.oldpassword.focus();
		return;
	}

	if(window.document.tmc.password1.value == "") {
		alert("Debe ingresar su Nueva Password");
		window.document.tmc.password1.focus();
		return;
	}
                                        
	if(window.document.tmc.password2.value == "") {
		alert("Debe re-ingresar su Nueva Password");
		window.document.tmc.password2.focus();
		return;
	}
 
	if(window.document.tmc.password2.value != window.document.tmc.password1.value) {
		alert("Las Passwords ingresadas no coinciden");
		window.document.tmc.password2.focus();
		return;
	}
 
	div = document.getElementById('resultado');
	oldpassword 	= document.tmc.oldpassword.value;
	password 		= document.tmc.password1.value;
                                        
	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("GET", "ajaxfuncs/sopPass.php?oldpassword="+oldpassword+"&password="+password,true);
	ajax.onreadystatechange=function() {  
		if (ajax.readyState==4) {         
			div.innerHTML = ajax.responseText
		}                                 
	}

	//ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send(null)
}              

//------------------------------------------------------------------------

function sopEmail () {

	if(window.document.tmc.name.value == "") {
		alert("Debe indicar un Nombre de Contacto");
		window.document.tmc.name.focus();
		return;
	}

	if(window.document.tmc.email.value == "") {
		alert("Debe indicar un Email de Contacto");
		window.document.tmc.email.focus();
		return;
	}

	if(window.document.tmc.contact.value == "") {
		alert("Debe indicar un Fono de Contacto");
		window.document.tmc.contact.focus();
		return;
	}

	if(window.document.tmc.subject.value == "") {
		alert("Debe indicar un Asunto");
		window.document.tmc.subject.focus();
		return;
	}
	
	if(isMessage(document.tmc.msgtext.value) == false) {
		return;
	}

	div 		= document.getElementById('resultado');
	subject 	= document.tmc.subject.value;
	email	 	= document.tmc.email.value;
	name 		= document.tmc.name.value;
	contact 	= document.tmc.contact.value;
	msgtext 	= document.tmc.msgtext.value;

	ajax=nuevoAjax();
	div.innerHTML = '<p style="text-align:center;"><img src="images/waiting.gif"></p>';
	ajax.open("POST", "ajaxfuncs/sopMail.php",true);
	ajax.onreadystatechange=function() {
		if (ajax.readyState==4) {
			div.innerHTML = ajax.responseText
	}

	document.tmc.subject.value="";
	document.tmc.name.value=""; 
	document.tmc.email.value=""; 
	document.tmc.contact.value=""; 
	document.tmc.msgtext.value=""; 
	}

	ajax.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
	ajax.send("subject="+subject+"&name="+name+"&email="+email+"&contact="+contact+"&msgtext="+msgtext)
}
function IsJsonString(str) {
       try {
           JSON.parse(str);
       } catch (e) {
           return false;
       }
       return true;
}
